describe('Main Menu Screen', () => {
  const login = 'http://localhost:9000';
  beforeEach(() => {
    cy.visit(login);
  });
  it('TC-01-01-01 (super admin)', () => {
    cy.get('[data-cy="login_username"]').type('superadmin');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(1)').should('contain', '<PERSON>');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Super Admin');
  });
  it('TC-01-01-02 (administrator)', () => {
    cy.get('[data-cy="login_username"]').type('administrator');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(1)').should('contain', 'Wave Tanaphat');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Administrator');
  });
  it('TC-01-01-03 (manager)', () => {
    cy.get('[data-cy="login_username"]').type('manager');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(1)').should('contain', 'Kob Worawit');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Manager');
  });
  it('TC-01-01-04 (editor)', () => {
    cy.get('[data-cy="login_username"]').type('editor');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(1)').should('contain', 'Yok Krittipak');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Editor');
  });
  it('TC-01-01-05 (standard user)', () => {
    cy.get('[data-cy="login_username"]').type('standard');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(1)').should('contain', 'Max jenmana');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Standard User');
  });
});
