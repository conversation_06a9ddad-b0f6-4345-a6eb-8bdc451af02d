describe('Login Screen', () => {
  const login = 'http://localhost:9000';
  beforeEach(() => {
    cy.visit(login);
  });
  it('TC-01-01-01', () => {
    cy.get('[data-cy="login_username"]').type('superadmin');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.url().should('include', '/home');
  });
  it('TC-01-01-02', () => {
    cy.get('[data-cy="login_username"]').type('superad');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.get('.q-notification').should('be.visible');
    cy.get('.q-notification').should('contain', 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
  });
  it('TC-01-01-03', () => {
    cy.get('[data-cy="login_username"]').type('superadmin');
    cy.get('[data-cy="login_password"]').type('1');
    cy.get('[data-cy="login_btn"]').click();
    cy.get('.q-notification').should('be.visible');
    cy.get('.q-notification').should('contain', 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
  });
  it('TC-01-01-04', () => {
    cy.get('[data-cy="login_username"]').type('superadmin');
    cy.get('[data-cy="login_password"]').type('1234');
    cy.get('[data-cy="login_btn"]').click();
    cy.get('.q-avatar__content > img').click();
    cy.get('.q-item--clickable').click();
    cy.url().should('include', '/login');
  });
});
