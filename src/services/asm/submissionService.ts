import { api as axios } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Submission } from 'src/types/models';

export class SubmissionService {
  private path = '/submissions';

  async getByAssessmentId(assessmentId: number): Promise<Submission> {
    try {
      const res = await axios.get<Submission>(`${this.path}/${assessmentId}`);
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ดึงข้อมูลสรุปล้มเหลว' });
      throw new Error('Get summary by assessmentId failed');
    }
  }

  async getDraft(assessmentId: number, userId: number): Promise<Submission> {
    try {
      const res = await axios.get<Submission>(`${this.path}/${assessmentId}/${userId}`);
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ดึงข้อมูลสรุปล้มเหลว' });
      throw new Error('Get summary by assessmentId failed');
    }
  }

  async getAll(): Promise<Submission[]> {
    try {
      const res = await axios.get<Submission[]>(this.path);
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ดึงข้อมูลสรุปทั้งหมดล้มเหลว' });
      throw new Error('Get all summaries failed');
    }
  }

  async create(data: Submission): Promise<Submission> {
    try {
      const payload = {
        ...data,
        endAt: '',
      };

      const res = await axios.post<Submission>(this.path, payload);
      Notify.create({ type: 'positive', message: 'สร้างรายงานสรุปสำเร็จ' });
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'มี Draft' });
      throw new Error('Create summary failed');
    }
  }

  async update(id: number, data: Submission): Promise<Submission> {
    try {
      const res = await axios.patch<Submission>(`${this.path}/submit-assessment/${id}`, data);
      Notify.create({ type: 'positive', message: 'อัปเดตรายงานสรุปสำเร็จ' });
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'อัปเดตรายงานสรุปล้มเหลว' });
      throw new Error('Update summary failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
      Notify.create({ type: 'positive', message: 'ลบรายงานสรุปสำเร็จ' });
    } catch {
      Notify.create({ type: 'negative', message: 'ลบรายงานสรุปล้มเหลว' });
      throw new Error('Remove summary failed');
    }
  }

  async startSubmission(linkUrl: string, userId: number): Promise<Submission> {
    try {
      const res = await axios.post<Submission>(`${this.path}/start-assessment`, {
        linkUrl,
        userId,
      });
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ไม่สามารถเริ่มการส่งทำแบบฟอร์มได้' });
      throw new Error('Start submission failed');
    }
  }
}

export const summaryService = new SubmissionService();
