import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Response } from 'src/types/models';
import type { ChartData } from 'src/types/chart';
import { isAxiosError } from 'axios';

export class ResponsesService {
  private path = '/responses';

  async create(params: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(this.path, params);
      return res.data;
    } catch {
      Notify.create({ message: 'ส่งคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Create response failed');
    }
  }

  async findAll(): Promise<Response[]> {
    const res = await api.get<Response[]>(this.path);
    return res.data;
  }

  async findOne(id: number): Promise<Response> {
    const res = await api.get<Response>(`${this.path}/${id}`);
    return res.data;
  }

  async findAnswer(submissionId: number, questionId: number): Promise<Response | null> {
    try {
      const res = await api.get<Response>(`${this.path}/${submissionId}/${questionId}`);
      return res.data;
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response?.status === 400) {
        return null;
      }

      console.error('Unexpected error:', error);
      throw error; // หรือ return null หากต้องการกลืน error
    }
  }

  async update(id: number, params: Response): Promise<Response> {
    try {
      const res = await api.patch<Response>(`${this.path}/${id}`, params);
      Notify.create({ message: 'อัปเดตคำตอบเรียบร้อย', type: 'positive' });
      return res.data;
    } catch {
      Notify.create({ message: 'อัปเดตคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Update response failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ message: 'ลบคำตอบเรียบร้อย', type: 'positive' });
    } catch {
      Notify.create({ message: 'ลบคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Remove response failed');
    }
  }

  async getChartData(assessmentId: number): Promise<ChartData[]> {
    const res = await api.get<ChartData[]>(`${this.path}/chart-data/${assessmentId}`);
    return res.data;
  }

  async saveUserQuizResponse(data: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(`${this.path}/quiz/save-response`, data);
      return res.data;
    } catch {
      Notify.create({ message: 'ส่งคำตอบไม่สำเร็จ', type: 'negative' });
      throw new Error('Submit quiz response failed');
    }
  }

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const res = await api.get<{ number: number }>(
      `/assessments${this.path}/header/${assessmentId}`,
    );
    return res.data.number;
  }

  // evaluate responseService
  async getResponseById(id: number): Promise<{ data: ChartData[] }> {
    try {
      // Use the same endpoint pattern as AssessmentService
      return await api.get(`assessments/dashboard/evaluate/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลแบบประเมินได้');
      throw error;
    }
  }

  async getResponseHeaderById(id: number) {
    try {
      // Use the same endpoint pattern as AssessmentService
      return await api.get(`assessments/header/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลหัวข้อคำตอบได้');
      throw error;
    }
  }
}

// evaluate responseService
const showError = (message: string) => {
  Notify.create({
    message: message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
