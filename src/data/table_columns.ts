import type { QTableColumn } from 'quasar';
import type { Assessment, Permission, User } from 'src/types/models';
import { formatDateDisplay } from 'src/utils/utils';

export const quizManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'left',
    field: (rows: { creator: { name: string } }) => rows.creator?.name ?? '-',
    sortable: true,
    style: 'min-width: 150px;',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด - ปิด',
    align: 'center' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center' as const,
    field: 'assessmentLink',
    sortable: false,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const evaluateManagementColumns = [
  { name: 'id', label: 'รหัส', align: 'center' as const, field: 'id', sortable: true },
  {
    name: 'title',
    label: 'ชื่อแบบสอบถาม',
    align: 'center' as const,
    field: 'name',
    sortable: true,
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'center' as const,
    field: (rows: { creator: { name: string } }) => rows.creator?.name ?? '-',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด-ปิด',
    align: 'center' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center' as const,
    field: (rows: { link: string }) => rows.link ?? '-',
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: () => '' },
];

export const userColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'center',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'center',
    label: 'ชื่อ',
    field: 'name',
  },
  { name: 'email', align: 'center', label: 'Email', field: 'email' },
  {
    name: 'role',
    align: 'center',
    label: 'บทบาท',
    field: (row: User) => row.roles?.map((role) => role.name).join(', ') || '-',
  },
  { name: 'actions', align: 'center', label: 'Actions', field: '' },
];

export const roleColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'center',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'center',
    label: 'ชื่อ',
    field: 'name',
  },
  { name: 'actions', align: 'center', label: 'Actions', field: '' },
];

export const permColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'center',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'center',
    label: 'ชื่อ',
    field: 'name',
  },
  {
    name: 'nameEn',
    align: 'center',
    label: 'ชื่ออังกฤษ',
    field: 'nameEn',
  },
  {
    name: 'status',
    align: 'center',
    label: 'สถานะ',
    field: (row: Permission) => (row.status ? 'ใช้งาน' : 'ไม่ใช้งาน'),
  },
  {
    name: 'default',
    align: 'center',
    label: 'ค่าเริ่มต้น',
    field: (row: Permission) => (row.isDefault ? 'ใช่' : 'ไม่ใช่'),
  },
  { name: 'actions', align: 'center', label: 'Actions', field: '' },
];
