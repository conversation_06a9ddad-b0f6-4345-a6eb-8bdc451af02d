import { Injectable } from '@nestjs/common';
import { extname } from 'path';
import * as sharp from 'sharp';
import { ApiService } from 'src/api/api.service';
import { UploadFileDto } from 'src/types/api/uploadFile';

@Injectable()
export class FileUploadService {
  constructor(private readonly apiService: ApiService) {}

  getNewFileName(originalName: string): string {
    return originalName.replace(extname(originalName), '.webp');
  }

  createWebpFile(
    file: Express.Multer.File,
    buffer: Buffer,
    newFileName: string,
  ): Express.Multer.File {
    return {
      ...file,
      buffer,
      originalname: newFileName,
      mimetype: 'image/webp',
      size: buffer.length,
      filename: newFileName,
    };
  }

  extractFileNameFromUrl(url: string): string | null { // อาจจะไม่ได้ใช้แล้ว
    if (!url) return null;
    try {
        const { pathname } = new URL(url);
        const pathParts = pathname.split('/');
        const fileIndex = pathParts.findIndex((p) => p === 'uploaded_files');
        return fileIndex !== -1 ? pathParts.slice(fileIndex).join('/') : null;
    } catch {
        return null;
    }
  }

  async getInputBuffer(file: Express.Multer.File): Promise<Buffer> {
    if (file.buffer) return file.buffer;
    if (file.path) {
      const fs = await import('fs/promises');
      return fs.readFile(file.path);
    }
    throw new Error('File input not found!');
  }

  async uploadAndGetImagePath(file: Express.Multer.File): Promise<string | null> {
    const inputBuffer = await this.getInputBuffer(file);
    const webpBuffer = await sharp(inputBuffer).webp({ quality: 80 }).toBuffer();
    const newFileName = this.getNewFileName(file.originalname);
    const webpFile = this.createWebpFile(file, webpBuffer, newFileName);

    const uploadFileDto: UploadFileDto = {
      path: '/uploaded_files',
      fileName: newFileName,
      fileType: 'webp',
    };

    const uploaded = await this.apiService.uploadFile(webpFile, uploadFileDto);
    // const uploadedFileName = uploaded?.result || uploaded?.fileUrl || uploaded?.url;
    // const publicFile = await this.apiService.getPublicFile(uploadedFileName, newFileName);
    // return publicFile?.result?.file_1?.view ?? null;

    return uploaded?.result ?? null; // แค่ชื่อไฟล์ที่อัปโหลด ตัวอย่าง เช่น uploaded_files/c8091aac-dd9a-461e-ad6c-ef08d4797f9f.webp
  }

  async getSignedUrl(fileName: string | null): Promise<string | null> {
    if (!fileName) return null;
    try {
      const publicFile = await this.apiService.getPublicFile(fileName);
      return publicFile?.result?.file_1?.view ?? null;
    } catch (error) {
      console.error('Failed to get signed URL:', error);
      return null;
    }
  }

  async deleteFileByUrl(fileName: string | null): Promise<void> { // เปลี่ยนจาก url เป็น fileName
    // const fileName = this.extractFileNameFromUrl(url);
    if (fileName) {
        await this.apiService.deleteFile(fileName);
    }
  }

}
