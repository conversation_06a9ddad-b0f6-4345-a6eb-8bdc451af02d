<template>
  <div>
    <!-- Error Banner -->
    <q-banner
      v-if="dashboardStore.error"
      inline-actions
      class="text-white bg-red q-mb-md rounded-borders"
    >
      <template v-slot:avatar>
        <q-icon name="error_outline" color="white" />
      </template>
      {{ dashboardStore.error }}
      <template v-slot:action>
        <q-btn flat color="white" label="Retry" @click="retryFetch" :loading="isLoading" />
        <q-btn flat color="white" label="Clear" @click="dashboardStore.clearError" />
      </template>
    </q-banner>

    <!-- QTable -->
    <q-table
      :rows="tableRows"
      :columns="columns"
      row-key="id"
      :loading="isLoading"
      v-model:pagination="paginationState"
      @request="handleTableRequest"
      :rows-per-page-options="[5, 10, 15, 25, 50]"
      binary-state-sort
      flat
      bordered
      separator="cell"
      class="q-mt-md"
      table-header-class="bg-primary text-black text-h6"
    >
      <template v-slot:body-cell-userName="{ row }">
        <q-td class="text-left">{{ row.userName }}</q-td>
      </template>

      <template v-slot:body-cell-date="{ row }">
        <q-td class="text-center">{{ formatDateTime(row.date) }}</q-td>
      </template>

      <template v-slot:body-cell-score="{ row }">
        <q-td
          :class="[
            'text-center',
            row.score < (dashboardStore.assessmentMeta?.highestScore ?? 100) / 2
              ? 'text-negative'
              : 'text-positive',
          ]"
        >
          {{ row.score }}
        </q-td>
      </template>

      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="article"
            @click="viewUserAnswer(row.id)"
            :loading="dashboardStore.isLoadingParticipantDetails"
            aria-label="View Answers"
          >
            <q-tooltip>ดูคำตอบ</q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data="{ icon, message, filter }">
        <div class="full-width row flex-center text-accent q-gutter-sm q-pa-md">
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
          <span>
            {{
              props.assessmentId === null && !isLoading
                ? 'Please select an assessment first.'
                : message
            }}
          </span>
        </div>
      </template>
    </q-table>

    <!-- Participant Details Dialog -->
    <q-dialog v-model="showParticipantDialog" persistent>
      <q-card style="min-width: 800px; max-width: 90vw">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">รายละเอียดผู้เข้าสอบ</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section v-if="dashboardStore.participantDetails">
          <div class="q-gutter-sm">
            <div>
              <strong>ชื่อผู้เข้าสอบ:</strong> {{ dashboardStore.participantDetails.userName }}
            </div>
            <div>
              <strong>คะแนนรวม:</strong> {{ dashboardStore.participantDetails.totalScore }}/{{
                dashboardStore.participantDetails.maxScore
              }}
              ({{ dashboardStore.participantDetails.scorePercentage }}%)
            </div>
            <div>
              <strong>เวลาเริ่ม:</strong>
              {{ formatDateTime(dashboardStore.participantDetails.startTime) }}
            </div>
            <div>
              <strong>เวลาสิ้นสุด:</strong>
              {{ formatDateTime(dashboardStore.participantDetails.endTime) }}
            </div>
          </div>

          <q-separator class="q-my-md" />

          <div class="text-h6 q-mb-md">รายละเอียดคำตอบ</div>
          <div
            v-for="question in dashboardStore.participantDetails.questions"
            :key="question.questionId"
            class="q-mb-md"
          >
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle1 q-mb-sm">
                  <strong>{{ question.questionSequence }}. {{ question.questionText }}</strong>
                </div>
                <div class="q-mb-sm">
                  <q-chip
                    :color="question.isCorrect ? 'positive' : 'negative'"
                    text-color="white"
                    :label="question.isCorrect ? 'ถูกต้อง' : 'ผิด'"
                  />
                  <span class="q-ml-sm">คะแนน: {{ question.score }}</span>
                </div>
                <div v-if="question.selectedOptionText" class="q-mb-sm">
                  <strong>คำตอบที่เลือก:</strong> {{ question.selectedOptionText }}
                </div>
                <div class="text-caption text-grey-7">
                  <strong>ตัวเลือกทั้งหมด:</strong>
                  <ul class="q-ma-none q-pl-md">
                    <li
                      v-for="option in question.options"
                      :key="option.id"
                      :class="option.isSelected ? 'text-primary' : ''"
                    >
                      {{ option.text }} {{ option.isSelected ? '(เลือก)' : '' }}
                    </li>
                  </ul>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </q-card-section>

        <q-card-section v-else-if="dashboardStore.isLoadingParticipantDetails">
          <div class="text-center">
            <q-circular-progress indeterminate size="50px" color="primary" />
            <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { ref, computed, onMounted, watch } from 'vue';
import type { QTableProps } from 'quasar';

import type { DataParams } from 'src/types/data';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';

const props = defineProps({
  assessmentId: {
    type: Number as PropType<number | null>,
    required: true,
  },
});

const dashboardStore = useQuizDashboardStore();
const showParticipantDialog = ref(false);
const searchTerm = ref<string>('');

const paginationState = ref<Required<QTableProps['pagination']>>({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// --- Computed Properties ---
const isLoading = computed(() => {
  return dashboardStore.isLoadingParticipants;
});

const tableRows = computed(() => {
  return dashboardStore.participants?.data || [];
});

// --- Columns Definition ---
const columns: QTableProps['columns'] = [
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    required: true,
  },
  {
    name: 'date',
    label: 'วันที่ทำ',
    align: 'center',
    field: 'date',
    sortable: true,
    required: true,
  },
  {
    name: 'userName',
    label: 'ชื่อผู้ทำแบบทดสอบ',
    align: 'left',
    field: 'userName',
    sortable: true,
    required: true,
  },
  {
    name: 'score',
    label: 'คะแนน',
    align: 'center',
    field: 'score',
    sortable: true,
    required: true,
  },
  {
    name: 'actions',
    label: 'กระดาษคำตอบ',
    align: 'center',
    field: () => '',
  },
];

// --- Methods ---
async function handleTableRequest(requestProps: {
  pagination: QTableProps['pagination'];
  filter?: string;
}) {
  const newPagination = requestProps.pagination;
  console.log('[QuizTakerList] handleTableRequest triggered. Assessment ID:', props.assessmentId);

  if (props.assessmentId === null) {
    console.warn('[QuizTakerList] handleTableRequest: assessmentId is null, aborting fetch.');
    return;
  }

  // Update local pagination state
  if (paginationState.value && newPagination) {
    paginationState.value.page = newPagination.page ?? 1;
    paginationState.value.rowsPerPage = newPagination.rowsPerPage ?? 10;
    paginationState.value.sortBy = newPagination.sortBy ?? null;
    paginationState.value.descending = newPagination.descending ?? false;
  }
  const params: DataParams = {
    page: paginationState.value?.page ?? 1,
    limit: paginationState.value?.rowsPerPage ?? 10,
    sortBy: paginationState.value?.sortBy ?? 'date',
    order: paginationState.value?.descending ? 'DESC' : 'ASC', // ✅ แก้ชื่อและ logic
    search: searchTerm.value ?? null, // ถ้ามีช่อง search
  };

  console.log('[QuizTakerList] Calling fetchParticipants with params:', params);
  await dashboardStore.fetchParticipants(props.assessmentId, params);
}

async function retryFetch() {
  console.log('[QuizTakerList] retryFetch called.');
  dashboardStore.clearError();
  if (props.assessmentId !== null && paginationState.value) {
    await handleTableRequest({ pagination: paginationState.value });
  }
}

async function viewUserAnswer(participantId: number) {
  console.log('[QuizTakerList] viewUserAnswer called for participant:', participantId);
  try {
    await dashboardStore.fetchParticipantDetails(participantId);
    showParticipantDialog.value = true;
  } catch (error) {
    console.error('Failed to fetch participant details:', error);
  }
}

function formatDateTime(dateString: string): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (e) {
    console.error('[QuizTakerList] Error formatting date:', e);
    return dateString;
  }
}

// --- Watchers ---
watch(
  () => props.assessmentId,
  async (newAssessmentId, oldAssessmentId) => {
    console.log(
      `[QuizTakerList] assessmentId watcher triggered. New: ${newAssessmentId}, Old: ${oldAssessmentId}`,
    );
    if (newAssessmentId !== oldAssessmentId) {
      if (paginationState.value) {
        paginationState.value.page = 1;
      }
      if (newAssessmentId !== null && paginationState.value) {
        await handleTableRequest({ pagination: paginationState.value });
      }
    }
  },
  { immediate: true },
);

watch(
  () => dashboardStore.participants,
  (newParticipants) => {
    console.log('[QuizTakerList] participants watcher triggered');
    if (paginationState.value) {
      if (newParticipants) {
        paginationState.value.rowsNumber = newParticipants.total;
        paginationState.value.page = newParticipants.curPage;
      } else {
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { deep: true },
);

// --- Lifecycle Hooks ---
onMounted(() => {
  console.log(`[QuizTakerList] Component mounted. Initial assessmentId: ${props.assessmentId}`);
});
</script>

<style scoped>
.edit-graph-icon {
  background-color: #673ab7;
  color: white;
  border-radius: 8px;
}

.edit-graph-icon:hover {
  background-color: #5e35b1;
}

:deep(.q-table thead th) {
  font-size: 1.1rem;
  padding-top: 16px;
  padding-bottom: 16px;
}

:deep(.q-table tbody td) {
  font-size: 1.1rem;
  padding-top: 12px;
  padding-bottom: 12px;
  vertical-align: middle;
  line-height: 1.6;
}

:deep(.q-table tbody td[data-label='รหัส']),
:deep(.q-table tbody td[data-label='คะแนน']) {
  text-align: center;
}

.bg-red .q-banner__content {
  font-size: 1rem;
}

.bg-red .q-btn {
  font-weight: 500;
}

:deep(.q-table__no-data span) {
  font-size: 1.1rem;
  color: #555;
}

:deep(.q-table__no-data .q-icon) {
  color: #777;
}
</style>
