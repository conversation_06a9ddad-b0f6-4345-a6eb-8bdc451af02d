<script setup lang="ts">
import EditorTool from 'src/components/common/EditorTool.vue';
import { computed, ref } from 'vue';
import type { ImageBody, ItemBlock } from 'src/types/models';
import type { CSSProperties } from 'vue';
import ItemBlockFooter from './ItemBlockFooter.vue';
import FloatImageBtn from '../FloatImageBtn.vue';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

defineEmits(['focus-fab', 'duplicate', 'delete', 'update:image']);
const globalStore = useGlobalStore();
const selectedFile = ref<File | null>(null);
const itemBlockId = ref(props.itemBlock.id || 0);
const imageId = ref(props.itemBlock.imageBody?.id || 0);

const imageText = ref(props.itemBlock.imageBody?.imageText || '');
const lastSavedImageText = ref(imageText.value);

const imgUrl = computed(() => props.itemBlock.imageBody?.imagePath || '');

const imageStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;
  let width: string | undefined;
  let height: string | undefined;
  if (body) {
    if (typeof body.imageWidth === 'number' && body.imageWidth > 0) {
      width = `${body.imageWidth}px`;
    }
    if (typeof body.imageHeight === 'number' && body.imageHeight > 0) {
      height = `${body.imageHeight}px`;
    }
  }
  return {
    width: width ?? 'auto',
    height: height ?? 'auto',
    maxWidth: '800px',
    maxHeight: '500px',
    overflowX: 'auto',
    overflowY: 'auto',
  };
});
async function performSaveImageText() {
  const imageBody = props.itemBlock.imageBody;
  const isCreate = !imageBody?.id;

  if (imageText.value.trim() === lastSavedImageText.value.trim() && !selectedFile.value) return;

  try {
    globalStore.startSaveOperation(isCreate ? 'Creating...' : 'Saving...');

    const service = new ImageBodyService();
    let updated: ImageBody;

    if (isCreate) {
      updated = await service.createImageBody(
        {
          itemBlockId: props.itemBlock.id,
          imageText: imageText.value,
          id: itemBlockId.value,
        },
        selectedFile.value!,
      );
    } else {
      updated = await service.updateImageBody(
        imageBody.id,
        {
          itemBlockId: props.itemBlock.id,
          imageText: imageText.value,
          id: imageId.value,
        },
        selectedFile.value ?? undefined,
      );
    }

    lastSavedImageText.value = updated.imageText ?? imageText.value;
    selectedFile.value = null; // เคลียร์ไฟล์หลังบันทึกเสร็จ

    globalStore.completeSaveOperation(
      true,
      isCreate ? 'Created successfully' : 'Saved successfully',
    );

    // if (isCreate) {
    //   emit('update:image', updated);
    // }
  } catch {
    globalStore.completeSaveOperation(false, isCreate ? 'Create failed' : 'Save failed');
  }
}
</script>

<template>
  <q-card class="q-pa-lg" style="min-height: 500px; max-height: 800px">
    <EditorTool
      class="q-mt-sm"
      label="ข้อความ..."
      :initialValue="imageText"
      @update:content="(val) => (imageText = val)"
      @blur="performSaveImageText"
    />
    <q-card-section v-if="imgUrl.length > 0">
      <div class="row justify-center">
        <div class="image-container">
          <img :src="imgUrl" alt="image" :style="imageStyle" />
          <FloatImageBtn class="pixel-image-position" />
        </div>
      </div>
    </q-card-section>
    <q-separator></q-separator>
    <q-card-section style="max-height: 50px">
      <ItemBlockFooter label="ข้อความ" style="margin-top: -10px" @delete="$emit('delete')" />
    </q-card-section>
  </q-card>
</template>
<style scoped>
.pixel-image-position {
  position: absolute;
  top: -16px; /* Position at the very top of the image */
  left: -32px; /* Position at the very left of the image */
  z-index: 10; /* Ensure it's above the image */
  margin: 0px; /* Add a small margin for visual spacing from the edge */
  cursor: pointer; /* Show pointer cursor on hover */
}

/* Add a container class to ensure proper positioning context */
.image-container {
  position: relative;
  display: inline-block; /* Contain the image and overlay */
  margin: 0 auto; /* Center the container */
}
</style>
